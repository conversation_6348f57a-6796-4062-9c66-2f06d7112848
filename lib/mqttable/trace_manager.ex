defmodule Mqttable.TraceManager do
  @moduledoc """
  Manager for MQTT trace messages storage using ETS tables.
  Each broker has its own ordered_set ETS table to store trace messages.
  Messages are stored with microsecond timestamp as the key for chronological ordering.

  ETS Table Structure:
  {microsecond_timestamp, client_id, type, direction, topic, payload, payload_length, qos, retain, packet_id, properties, reason_code, extra_info, formatted_timestamp}

  This granular structure enables efficient filtering by individual fields without loading entire message maps.
  """
  use GenServer
  require Logger

  # Maximum number of messages to keep per broker
  @max_messages_per_broker 10000

  # Client API

  @doc """
  Starts the trace manager.
  """
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  Stores a trace message for a specific broker.
  """
  def store_message(broker_name, message) when is_binary(broker_name) and is_map(message) do
    GenServer.cast(__MODULE__, {:store_message, broker_name, message})
  end

  @doc """
  Stores a trace message by client_id, automatically finding the corresponding broker.
  """
  def store_message_by_client_id(client_id, message)
      when is_binary(client_id) and is_map(message) do
    case find_broker_by_client_id(client_id) do
      {:ok, broker_name} ->
        store_message(broker_name, message)

      {:error, _reason} ->
        # If we can't find the broker, store it under a default "unknown" broker
        store_message("unknown", message)
    end
  end

  @doc """
  Gets all trace messages for a specific broker, ordered by timestamp.
  """
  def get_messages(broker_name) when is_binary(broker_name) do
    GenServer.call(__MODULE__, {:get_messages, broker_name})
  end

  @doc """
  Gets trace messages for a specific broker with pagination support.
  Returns messages ordered by timestamp (newest first).

  ## Parameters
  - broker_name: The name of the broker
  - limit: Maximum number of messages to return (default: 50)
  - offset: Number of messages to skip (default: 0)

  ## Returns
  - {:ok, messages, has_more?} where has_more? indicates if there are more messages available
  """
  def get_messages_paginated(broker_name, limit \\ 50, offset \\ 0)
      when is_binary(broker_name) and is_integer(limit) and is_integer(offset) do
    GenServer.call(__MODULE__, {:get_messages_paginated, broker_name, limit, offset})
  end

  @doc """
  Gets filtered trace messages for a specific broker with pagination support.
  Returns messages ordered by timestamp (newest first).

  ## Parameters
  - broker_name: The name of the broker
  - filters: Map of filters to apply (e.g., %{client_id: "client1", type: "PUBLISH"})
  - limit: Maximum number of messages to return (default: 50)
  - offset: Number of messages to skip (default: 0)

  ## Returns
  - {:ok, messages, has_more?} where has_more? indicates if there are more messages available
  """
  def get_messages_filtered(broker_name, filters, limit \\ 50, offset \\ 0)
      when is_binary(broker_name) and is_map(filters) and is_integer(limit) and
             is_integer(offset) do
    GenServer.call(__MODULE__, {:get_messages_filtered, broker_name, filters, limit, offset})
  end

  @doc """
  Clears all trace messages for a specific broker.
  """
  def clear_messages(broker_name) when is_binary(broker_name) do
    GenServer.cast(__MODULE__, {:clear_messages, broker_name})
  end

  @doc """
  Gets the list of all broker names that have trace tables.
  """
  def get_broker_names do
    GenServer.call(__MODULE__, :get_broker_names)
  end

  @doc """
  Removes the trace table for a specific broker.
  """
  def remove_broker(broker_name) when is_binary(broker_name) do
    GenServer.cast(__MODULE__, {:remove_broker, broker_name})
  end

  # Server Callbacks

  @impl true
  def init(_opts) do
    # Initialize state to track broker tables
    state = %{
      broker_tables: %{}
    }

    {:ok, state}
  end

  @impl true
  def handle_call({:get_messages, broker_name}, _from, state) do
    table_name = get_table_name(broker_name)

    messages =
      case Map.get(state.broker_tables, broker_name) do
        nil ->
          []

        _table_ref ->
          # Get all messages from the ETS table, ordered by timestamp
          :ets.tab2list(table_name)
          |> Enum.sort_by(
            fn {timestamp, _, _, _, _, _, _, _, _, _, _, _, _, _} -> timestamp end,
            :desc
          )
          |> Enum.map(&convert_record_to_message/1)
      end

    {:reply, messages, state}
  end

  @impl true
  def handle_call({:get_messages_paginated, broker_name, limit, offset}, _from, state) do
    table_name = get_table_name(broker_name)

    result =
      case Map.get(state.broker_tables, broker_name) do
        nil ->
          {:ok, [], false}

        _table_ref ->
          # Get all messages from the ETS table, ordered by timestamp (newest first)
          all_messages =
            :ets.tab2list(table_name)
            |> Enum.sort_by(
              fn {timestamp, _, _, _, _, _, _, _, _, _, _, _, _, _} -> timestamp end,
              :desc
            )
            |> Enum.map(&convert_record_to_message/1)

          total_count = length(all_messages)

          # Apply pagination
          messages =
            all_messages
            |> Enum.drop(offset)
            |> Enum.take(limit)

          # Check if there are more messages available
          has_more = offset + limit < total_count

          {:ok, messages, has_more}
      end

    {:reply, result, state}
  end

  @impl true
  def handle_call({:get_messages_filtered, broker_name, filters, limit, offset}, _from, state) do
    table_name = get_table_name(broker_name)

    result =
      case Map.get(state.broker_tables, broker_name) do
        nil ->
          {:ok, [], false}

        _table_ref ->
          # Get all messages from the ETS table, ordered by timestamp (newest first)
          all_messages =
            :ets.tab2list(table_name)
            |> Enum.sort_by(
              fn {timestamp, _, _, _, _, _, _, _, _, _, _, _, _, _} -> timestamp end,
              :desc
            )
            |> Enum.filter(&matches_filters?(&1, filters))
            |> Enum.map(&convert_record_to_message/1)

          total_count = length(all_messages)

          # Apply pagination
          messages =
            all_messages
            |> Enum.drop(offset)
            |> Enum.take(limit)

          # Check if there are more messages available
          has_more = offset + limit < total_count

          {:ok, messages, has_more}
      end

    {:reply, result, state}
  end

  @impl true
  def handle_call(:get_broker_names, _from, state) do
    broker_names = Map.keys(state.broker_tables)
    {:reply, broker_names, state}
  end

  @impl true
  def handle_call(_request, _from, state) do
    {:reply, :ok, state}
  end

  @impl true
  def handle_cast({:store_message, broker_name, message}, state) do
    # Ensure the table exists for this broker
    new_state = ensure_table_exists(broker_name, state)

    table_name = get_table_name(broker_name)

    # Use microsecond timestamp as key for ordering (from message.id)
    timestamp_key = message.id || DateTime.to_unix(DateTime.utc_now(), :microsecond)

    # Store message in granular structure for better filtering
    trace_record = {
      timestamp_key,
      message.client_id,
      message.type,
      message.direction,
      message.topic,
      message.payload,
      message.payload_length,
      message.qos,
      message.retain,
      message.packet_id,
      message.properties,
      message.reason_code,
      message.extra_info,
      message.timestamp
    }

    # Insert the message
    :ets.insert(table_name, trace_record)

    # Check if we need to clean up old messages
    cleanup_old_messages(table_name)

    {:noreply, new_state}
  end

  @impl true
  def handle_cast({:clear_messages, broker_name}, state) do
    table_name = get_table_name(broker_name)

    case Map.get(state.broker_tables, broker_name) do
      nil ->
        :ok

      _table_ref ->
        :ets.delete_all_objects(table_name)
    end

    {:noreply, state}
  end

  @impl true
  def handle_cast({:remove_broker, broker_name}, state) do
    table_name = get_table_name(broker_name)

    case Map.get(state.broker_tables, broker_name) do
      nil ->
        {:noreply, state}

      _table_ref ->
        :ets.delete(table_name)
        new_broker_tables = Map.delete(state.broker_tables, broker_name)
        {:noreply, %{state | broker_tables: new_broker_tables}}
    end
  end

  @impl true
  def handle_cast(_request, state) do
    {:noreply, state}
  end

  # Private helper functions

  # Convert ETS record back to message map format
  defp convert_record_to_message({
         timestamp_key,
         client_id,
         type,
         direction,
         topic,
         payload,
         payload_length,
         qos,
         retain,
         packet_id,
         properties,
         reason_code,
         extra_info,
         formatted_timestamp
       }) do
    %{
      id: timestamp_key,
      timestamp: formatted_timestamp,
      client_id: client_id,
      type: type,
      direction: direction,
      topic: topic,
      payload: payload,
      payload_length: payload_length,
      qos: qos,
      retain: retain,
      packet_id: packet_id,
      properties: properties,
      reason_code: reason_code,
      extra_info: extra_info
    }
  end

  # Check if a record matches the given filters
  defp matches_filters?(
         {_timestamp, client_id, type, direction, topic, payload, _payload_length, qos, retain,
          packet_id, _properties, _reason_code, _extra_info, _formatted_timestamp},
         filters
       ) do
    Enum.all?(filters, fn {filter_key, filter_value} ->
      case filter_key do
        :client_id -> client_id == filter_value
        :type -> type == filter_value
        :direction -> direction == filter_value
        :topic -> topic && String.contains?(topic, filter_value)
        :payload -> payload && String.contains?(payload, filter_value)
        :qos -> qos == filter_value
        :retain -> retain == filter_value
        :packet_id -> packet_id == filter_value
        _ -> true
      end
    end)
  end

  # Find broker name by client_id
  defp find_broker_by_client_id(client_id) do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Search through all connection sets to find the one containing this client_id
    result =
      Enum.find_value(connection_sets, fn set ->
        # Check if any connection in this set has the matching client_id
        connection =
          Enum.find(set[:connections] || [], fn conn ->
            Map.get(conn, :client_id) == client_id
          end)

        if connection do
          set[:name]
        else
          nil
        end
      end)

    case result do
      nil -> {:error, :broker_not_found}
      broker_name -> {:ok, broker_name}
    end
  end

  defp get_table_name(broker_name) do
    String.to_atom("trace_#{broker_name}")
  end

  defp ensure_table_exists(broker_name, state) do
    case Map.get(state.broker_tables, broker_name) do
      nil ->
        # Create new ETS table for this broker
        table_name = get_table_name(broker_name)
        table_ref = :ets.new(table_name, [:ordered_set, :public, :named_table])

        Logger.info("Created trace table for broker: #{broker_name}")

        new_broker_tables = Map.put(state.broker_tables, broker_name, table_ref)
        %{state | broker_tables: new_broker_tables}

      _table_ref ->
        # Table already exists
        state
    end
  end

  defp cleanup_old_messages(table_name) do
    # Get the current number of messages
    message_count = :ets.info(table_name, :size)

    if message_count > @max_messages_per_broker do
      # Get all messages ordered by timestamp (oldest first)
      all_messages =
        :ets.tab2list(table_name)
        |> Enum.sort_by(
          fn {timestamp, _, _, _, _, _, _, _, _, _, _, _, _, _} -> timestamp end,
          :asc
        )

      # Calculate how many to delete
      messages_to_delete = message_count - @max_messages_per_broker

      # Delete the oldest messages
      all_messages
      |> Enum.take(messages_to_delete)
      |> Enum.each(fn {timestamp, _, _, _, _, _, _, _, _, _, _, _, _, _} ->
        :ets.delete(table_name, timestamp)
      end)

      Logger.debug("Cleaned up #{messages_to_delete} old messages from #{table_name}")
    end
  end
end

defmodule MqttableWeb.TraceComponent do
  @moduledoc """
  LiveComponent for displaying MQTT trace data in a red-themed section.
  This component displays trace messages in a table format and provides
  functionality for viewing message details.
  """
  use MqttableWeb, :live_component
  import Bitwise

  # Maximum number of messages to display in the table (15 rows visible)
  @max_display_messages 15
  # Number of messages to load per page for infinite scroll
  @messages_per_page 25

  @impl true
  def update(assigns, socket) do
    # Initialize or update the stream if trace_messages changed
    socket =
      if Map.has_key?(assigns, :trace_messages) &&
           assigns.trace_messages != socket.assigns[:trace_messages] do
        # Check if this is a new message being added (incremental update)
        old_messages = socket.assigns[:trace_messages] || []
        new_messages = assigns.trace_messages

        if length(new_messages) > length(old_messages) && length(old_messages) > 0 do
          # This is an incremental update - add only the new messages
          new_message_count = length(new_messages) - length(old_messages)
          newly_added_messages = Enum.take(new_messages, -new_message_count)

          socket =
            socket
            |> assign(assigns)

          # Add new messages to the end of the stream (bottom of table)
          Enum.reduce(newly_added_messages, socket, fn message, acc_socket ->
            stream_insert(acc_socket, :trace_messages, message, at: -1)
          end)
        else
          # This is a full reset - replace all messages
          limited_messages =
            new_messages
            # Keep some buffer for smooth scrolling
            |> Enum.take(@max_display_messages * 2)

          socket
          |> assign(assigns)
          |> stream(:trace_messages, limited_messages, reset: true)
          |> assign(:page_offset, 0)
          |> assign(:has_more_messages, true)
          |> assign(:loading_more, false)
          |> assign(:visible_message_count, min(length(limited_messages), @max_display_messages))
        end
      else
        assign(socket, assigns)
      end

    socket =
      socket
      |> assign_new(:active_tab, fn -> "message_details" end)
      |> assign_new(:publish_form, fn -> default_publish_form() end)
      |> assign_new(:selected_message, fn -> nil end)
      |> assign_new(:topic_payload_filter, fn -> "" end)
      |> assign_new(:client_id_filter, fn -> "" end)
      |> assign_new(:direction_filter, fn -> "all" end)
      |> assign_new(:ignore_ping, fn -> false end)
      |> assign_new(:page_offset, fn -> 0 end)
      |> assign_new(:has_more_messages, fn -> true end)
      |> assign_new(:loading_more, fn -> false end)
      |> assign_new(:visible_message_count, fn -> 0 end)
      |> assign_new(:payload_view_type, fn -> "plaintext" end)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    # Get the total count of messages in the stream for display
    # For streams, we need to count the inserts
    total_messages =
      case assigns.streams.trace_messages do
        %Phoenix.LiveView.LiveStream{inserts: inserts} -> length(inserts)
        _ -> 0
      end

    assigns =
      assigns
      |> assign(:total_messages, total_messages)

    ~H"""
    <div class="flex-grow flex flex-col overflow-hidden gap-2 main-content-area">
      <!-- trace section -->
      <div class="w-full flex flex-col bg-base-100 p-2">
        <!-- Filter controls -->
        <div class="mb-4">
          <!-- First row: Topic/Payload filter and Clear button -->
          <div class="flex justify-between items-center mb-2">
            <input
              type="text"
              placeholder="Filter by topic or payload..."
              class="input input-sm input-bordered flex-grow mr-2"
              value={@topic_payload_filter}
              phx-keyup="topic_payload_filter_changed"
              phx-target={@myself}
            />
            <button
              class="btn btn-sm btn-outline btn-error"
              phx-click="clear_trace"
              phx-target={@myself}
            >
              <.icon name="hero-trash" class="size-4 mr-1" /> Clear
            </button>
          </div>
          
    <!-- Second row: Client ID, Direction filters and Ignore PING checkbox -->
          <div class="grid grid-cols-1 sm:grid-cols-3 gap-2">
            <!-- Client ID Filter -->
            <input
              type="text"
              placeholder="Filter by Client ID..."
              class="input input-sm input-bordered"
              value={@client_id_filter}
              phx-keyup="client_id_filter_changed"
              phx-target={@myself}
            />
            
    <!-- Direction Filter -->
            <select
              class="select select-sm select-bordered"
              phx-change="direction_filter_changed"
              phx-target={@myself}
            >
              <option value="all" selected={@direction_filter == "all"}>All Directions</option>
              <option value="IN" selected={@direction_filter == "IN"}>IN</option>
              <option value="OUT" selected={@direction_filter == "OUT"}>OUT</option>
            </select>
            
    <!-- Ignore PING Checkbox -->
            <div class="form-control">
              <label class="label cursor-pointer justify-start">
                <input
                  type="checkbox"
                  class="checkbox checkbox-sm"
                  checked={@ignore_ping}
                  phx-click="toggle_ignore_ping"
                  phx-target={@myself}
                />
                <span class="label-text ml-2">Ignore PING packets</span>
              </label>
            </div>
          </div>
        </div>
        
    <!-- Trace message table -->
        <div class="trace-message-table-container">
          <div class="trace-message-table-wrapper">
            <div id="trace-table-highlight-container" phx-hook="TraceRowHighlight">
              <table class="table table-xs table-pin-rows table-pin-cols table-zebra w-full compact">
                <thead>
                  <tr>
                    <th class="w-16">Packet ID</th>
                    <th class="w-36">Timestamp</th>
                    <th class="w-32">ClientID</th>
                    <th class="w-20">Direction</th>
                    <th class="w-24">Type</th>
                    <th class="w-32">Topic</th>
                    <th class="w-1/3">Payload (Preview)</th>
                    <th class="w-16">Length</th>
                    <th class="w-12">QoS</th>
                    <th class="w-16">Retain</th>
                  </tr>
                </thead>
                <tbody
                  id="trace-message-table-body"
                  phx-update="stream"
                  phx-viewport-bottom="load-more-messages"
                  phx-target={@myself}
                  phx-hook="TraceTableSmartScroll"
                >
                  <%= for {id, message} <- @streams.trace_messages do %>
                    <tr
                      id={id}
                      class={"hover cursor-pointer trace-message-row #{direction_indicator_class(message.direction)} #{if @selected_message && to_string(@selected_message.id) == to_string(message.id), do: "trace-message-selected", else: ""}"}
                      phx-click="select_message"
                      phx-value-id={message.id}
                      phx-target={@myself}
                    >
                      <td>
                        <span class="trace-packet-id">{message.packet_id || "N/A"}</span>
                      </td>
                      <td>
                        <span class="trace-timestamp">{format_timestamp(message.timestamp)}</span>
                      </td>
                      <td>
                        <span class="badge badge-ghost badge-sm trace-client-id">
                          {message.client_id || "unknown"}
                        </span>
                      </td>
                      <td>
                        <span class={"badge badge-sm #{direction_badge_class(message.direction)} trace-direction"}>
                          <.icon name={direction_icon(message.direction)} class="size-3 mr-1" />
                          {message.direction || "N/A"}
                        </span>
                      </td>
                      <td>
                        <span class={"badge badge-sm badge-outline #{message_type_badge_class(message.type)} trace-message-type"}>
                          {message.type}
                        </span>
                      </td>
                      <td class="table-cell-truncate" title={message.topic}>
                        <span class="trace-topic">{message.topic || "N/A"}</span>
                      </td>
                      <td class="payload-preview-truncate font-mono" title={message.payload}>
                        <span class="trace-payload">
                          {String.slice(message.payload || "", 0, 100) <>
                            if String.length(message.payload || "") > 100, do: "...", else: ""}
                        </span>
                      </td>
                      <td>
                        <span class="trace-payload-length">{message.payload_length}</span>
                      </td>
                      <td>
                        <span class="trace-qos">{message.qos}</span>
                      </td>
                      <td>
                        <span class="trace-retain">{if message.retain, do: "Yes", else: "No"}</span>
                      </td>
                    </tr>
                  <% end %>
                  
    <!-- Loading indicator row -->
                  <%= if @loading_more && @has_more_messages do %>
                    <tr id="loading-more-row" class="trace-loading-row">
                      <td colspan="10" class="text-center py-4">
                        <span class="loading loading-spinner loading-sm"></span>
                        <span class="ml-2 text-sm text-gray-500">Loading more messages...</span>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
    <!-- Visual separator line -->
      <div class="border-t border-base-300"></div>
      
    <!-- Tabbed interface below trace table -->
      <div class="w-full flex flex-col">
        <!-- Tab navigation -->
        <div class="tabs tabs-bordered">
          <button
            class={"tab tab-bordered #{if @active_tab == "message_details", do: "tab-active", else: ""}"}
            phx-click="switch_tab"
            phx-value-tab="message_details"
            phx-target={@myself}
          >
            <.icon name="hero-document-text" class="size-4 mr-2" /> Message Details
          </button>
          <button
            class={"tab tab-bordered #{if @active_tab == "send_message", do: "tab-active", else: ""}"}
            phx-click="switch_tab"
            phx-value-tab="send_message"
            phx-target={@myself}
          >
            <.icon name="hero-paper-airplane" class="size-4 mr-2" /> Send Message
          </button>
        </div>
        
    <!-- Tab content -->
        <div class="flex-grow p-2 rounded-lg shadow trace-details-panel overflow-y-auto">
          <%= if @active_tab == "message_details" do %>
            <!-- Message Details Tab Content -->
            <%= if @selected_message do %>
              {render_packet_details(@selected_message, @myself, @payload_view_type)}
            <% else %>
              <p class="text-center text-gray-500">Select a message from the table to view details</p>
            <% end %>
          <% else %>
            <!-- Send Message Tab Content -->
            <div class="space-y-4">
              <h3 class="text-lg font-semibold mb-4">Send MQTT Message</h3>

              <.form
                for={@publish_form}
                phx-submit="send_message"
                phx-target={@myself}
                class="space-y-4"
              >
                <!-- Client ID Selection -->
                <div class="form-control w-full">
                  <label class="label">
                    <span class="label-text font-medium">
                      Client <span class="text-error">*</span>
                    </span>
                  </label>
                  <select name="client_id" class="select select-bordered w-full" required>
                    <option value="">Select a connected client</option>
                    <%= for client <- get_connected_clients(@active_broker_name || "") do %>
                      <option
                        value={client.client_id}
                        selected={@publish_form["client_id"] == client.client_id}
                      >
                        {client.client_id}
                      </option>
                    <% end %>
                  </select>
                </div>
                
    <!-- Topic -->
                <div class="form-control w-full">
                  <label class="label">
                    <span class="label-text font-medium">
                      Topic <span class="text-error">*</span>
                    </span>
                  </label>
                  <input
                    type="text"
                    name="topic"
                    value={@publish_form["topic"]}
                    placeholder="Enter topic (e.g., 'device/sensor/temperature')"
                    class="input input-bordered w-full"
                    required
                  />
                </div>
                
    <!-- Payload -->
                <div class="form-control w-full">
                  <label class="label">
                    <span class="label-text font-medium">Payload</span>
                  </label>
                  <textarea
                    name="payload"
                    placeholder="Enter message payload"
                    class="textarea textarea-bordered w-full h-32"
                  ><%= @publish_form["payload"] %></textarea>
                </div>
                
    <!-- QoS and Retain in one row -->
                <div class="grid grid-cols-2 gap-4">
                  <!-- QoS -->
                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">QoS</span>
                    </label>
                    <select name="qos" class="select select-bordered w-full">
                      <option value="0" selected={@publish_form["qos"] == 0}>
                        QoS 0 (At most once)
                      </option>
                      <option value="1" selected={@publish_form["qos"] == 1}>
                        QoS 1 (At least once)
                      </option>
                      <option value="2" selected={@publish_form["qos"] == 2}>
                        QoS 2 (Exactly once)
                      </option>
                    </select>
                  </div>
                  
    <!-- Retain Flag -->
                  <div class="form-control w-full">
                    <label class="label">
                      <span class="label-text font-medium">Retain</span>
                    </label>
                    <div class="flex items-center gap-2 mt-2">
                      <input
                        type="checkbox"
                        name="retain"
                        checked={@publish_form["retain"]}
                        class="toggle toggle-primary"
                      />
                      <span class="label-text">Retain message</span>
                    </div>
                  </div>
                </div>
                
    <!-- Submit Button -->
                <div class="form-control w-full">
                  <button type="submit" class="btn btn-primary">
                    <.icon name="hero-paper-airplane" class="size-4 mr-2" /> Send Message
                  </button>
                </div>
              </.form>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("load-more-messages", _params, socket) do
    # Only load more if we're not already loading and there are more messages
    if socket.assigns.loading_more || !socket.assigns.has_more_messages do
      {:noreply, socket}
    else
      # Set loading state
      socket = assign(socket, :loading_more, true)

      # Get the active broker name
      broker_name = socket.assigns[:active_broker_name]

      if broker_name do
        # Calculate the new offset (current number of messages in stream)
        current_count =
          case socket.assigns.streams.trace_messages do
            %Phoenix.LiveView.LiveStream{inserts: inserts} -> length(inserts)
            _ -> 0
          end

        new_offset = current_count

        # Build filters map from current filter states
        filters = build_filters_map(socket.assigns)

        # Load more messages using pagination with filtering
        case Mqttable.TraceManager.get_messages_filtered(
               broker_name,
               filters,
               @messages_per_page,
               new_offset
             ) do
          {:ok, new_messages, has_more} ->
            # Get current stream messages to manage the display limit
            current_stream_messages =
              case socket.assigns.streams.trace_messages do
                %Phoenix.LiveView.LiveStream{inserts: inserts} ->
                  Enum.map(inserts, fn {_id, message} -> message end)

                _ ->
                  []
              end

            # Combine current and new messages (append new messages to end)
            all_messages = current_stream_messages ++ new_messages

            # Keep only the most recent messages within our buffer limit
            # This prevents the table from growing indefinitely
            # Keep 3x the display limit as buffer
            buffer_limit = @max_display_messages * 3
            limited_messages = Enum.take(all_messages, buffer_limit)

            # Update the stream with the limited messages
            socket =
              socket
              |> stream(:trace_messages, limited_messages, reset: true)
              |> assign(:has_more_messages, has_more)
              |> assign(:loading_more, false)
              |> assign(:page_offset, new_offset + length(new_messages))
              |> assign(
                :visible_message_count,
                min(length(limited_messages), @max_display_messages)
              )

            {:noreply, socket}

          _error ->
            # Handle error case
            socket =
              socket
              |> assign(:loading_more, false)
              |> put_flash(:error, "Failed to load more messages")

            {:noreply, socket}
        end
      else
        # No broker name available
        socket = assign(socket, :loading_more, false)
        {:noreply, socket}
      end
    end
  end

  @impl true
  def handle_event("select_message", %{"id" => id}, socket) do
    # Find the selected message by ID
    id = String.to_integer(id)

    # Try to find the message from both the original trace_messages and the stream
    # First try to find from the original trace_messages list
    selected_message =
      case socket.assigns[:trace_messages] do
        messages when is_list(messages) ->
          Enum.find(messages, fn message -> message.id == id end)

        _ ->
          # Fallback: try to find from the stream
          case socket.assigns.streams.trace_messages do
            %Phoenix.LiveView.LiveStream{inserts: inserts} ->
              inserts
              |> Enum.find_value(fn {_stream_id, message} ->
                if message.id == id, do: message, else: nil
              end)

            _ ->
              nil
          end
      end

    # Auto-switch to message details tab when a message is selected
    socket =
      socket
      |> assign(:selected_message, selected_message)
      |> assign(:active_tab, "message_details")

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_trace", _params, socket) do
    # Get the active broker name from the parent assigns
    broker_name = socket.assigns[:active_broker_name]

    # Clear the trace messages from ETS table if broker name is available
    if broker_name do
      Mqttable.TraceManager.clear_messages(broker_name)
    end

    # Clear the stream and reset state
    socket =
      socket
      |> stream(:trace_messages, [], reset: true)
      |> assign(:selected_message, nil)
      |> assign(:page_offset, 0)
      |> assign(:has_more_messages, true)
      |> assign(:loading_more, false)

    {:noreply, socket}
  end

  @impl true
  def handle_event("topic_payload_filter_changed", %{"value" => value}, socket) do
    # Update the topic/payload filter and apply filtering
    socket = assign(socket, :topic_payload_filter, value)
    apply_filters_and_reload(socket)
  end

  @impl true
  def handle_event("client_id_filter_changed", %{"value" => value}, socket) do
    # Update the client ID filter and apply filtering
    socket = assign(socket, :client_id_filter, value)
    apply_filters_and_reload(socket)
  end

  @impl true
  def handle_event("direction_filter_changed", %{"value" => value}, socket) do
    # Update the direction filter and apply filtering
    socket = assign(socket, :direction_filter, value)
    apply_filters_and_reload(socket)
  end

  @impl true
  def handle_event("toggle_ignore_ping", _params, socket) do
    # Toggle the ignore PING filter and apply filtering
    socket = assign(socket, :ignore_ping, !socket.assigns.ignore_ping)
    apply_filters_and_reload(socket)
  end

  @impl true
  def handle_event("switch_payload_view", %{"type" => type}, socket) do
    # Switch payload view type between plaintext, json, and hex
    valid_types = ["plaintext", "json", "hex"]

    payload_view_type = if type in valid_types, do: type, else: "plaintext"

    {:noreply, assign(socket, :payload_view_type, payload_view_type)}
  end

  @impl true
  def handle_event("view_payload_as_text", _params, socket) do
    # Legacy event handler - redirect to new switch handler
    {:noreply, assign(socket, :payload_view_type, "plaintext")}
  end

  @impl true
  def handle_event("view_payload_as_json", _params, socket) do
    # Legacy event handler - redirect to new switch handler
    {:noreply, assign(socket, :payload_view_type, "json")}
  end

  @impl true
  def handle_event("view_payload_as_hex", _params, socket) do
    # Legacy event handler - redirect to new switch handler
    {:noreply, assign(socket, :payload_view_type, "hex")}
  end

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    # Switch to the selected tab and auto-switch to message details when a message is selected
    active_tab =
      if tab == "message_details" && socket.assigns.selected_message,
        do: "message_details",
        else: tab

    {:noreply, assign(socket, :active_tab, active_tab)}
  end

  @impl true
  def handle_event("send_message", params, socket) do
    # Extract form parameters
    client_id = params["client_id"]
    topic = params["topic"]
    payload = params["payload"] || ""
    qos = String.to_integer(params["qos"] || "0")
    retain = params["retain"] == "on"

    # Validate required fields
    if client_id != "" && topic != "" do
      # Attempt to publish the message
      case Mqttable.MqttClient.Manager.publish(client_id, topic, payload,
             qos: qos,
             retain: retain
           ) do
        {:ok, packet_id} ->
          # Success - show success message and reset form
          socket =
            socket
            |> put_flash(:info, "Message sent successfully (Packet ID: #{packet_id})")
            |> assign(:publish_form, default_publish_form())

          {:noreply, socket}

        {:error, :not_connected} ->
          socket = put_flash(socket, :error, "Client is not connected")
          {:noreply, socket}

        {:error, _reason, error_message} ->
          socket = put_flash(socket, :error, "Failed to send message: #{error_message}")
          {:noreply, socket}
      end
    else
      # Validation failed
      socket = put_flash(socket, :error, "Please fill in all required fields")
      {:noreply, socket}
    end
  end

  # Helper function to apply filters and reload messages
  defp apply_filters_and_reload(socket) do
    broker_name = socket.assigns[:active_broker_name]

    if broker_name do
      # Build filters map from current filter states
      filters = build_filters_map(socket.assigns)

      # Get filtered messages from TraceManager
      case Mqttable.TraceManager.get_messages_filtered(
             broker_name,
             filters,
             @messages_per_page,
             0
           ) do
        {:ok, filtered_messages, has_more} ->
          socket =
            socket
            |> stream(:trace_messages, filtered_messages, reset: true)
            |> assign(:has_more_messages, has_more)
            |> assign(:page_offset, 0)
            |> assign(:loading_more, false)
            |> assign(
              :visible_message_count,
              min(length(filtered_messages), @max_display_messages)
            )

          {:noreply, socket}

        _error ->
          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  # Helper function to build filters map from assigns
  defp build_filters_map(assigns) do
    filters = %{}

    # Add topic/payload filter if not empty
    filters =
      if assigns.topic_payload_filter != "" do
        # For topic/payload filter, we'll check both topic and payload in the TraceManager
        filters
        |> Map.put(:topic, assigns.topic_payload_filter)
        |> Map.put(:payload, assigns.topic_payload_filter)
      else
        filters
      end

    # Add client ID filter if not empty
    filters =
      if assigns.client_id_filter != "" do
        Map.put(filters, :client_id, assigns.client_id_filter)
      else
        filters
      end

    # Add direction filter if not "all"
    filters =
      if assigns.direction_filter != "all" do
        Map.put(filters, :direction, assigns.direction_filter)
      else
        filters
      end

    # Add ignore PING filter if enabled
    filters =
      if assigns.ignore_ping do
        Map.put(filters, :ignore_ping, true)
      else
        filters
      end

    filters
  end

  # Helper function to get connected clients for a broker
  defp get_connected_clients(_broker_name) do
    # Get all connected clients from the MQTT client manager
    # Note: Currently we don't filter by broker_name, but get all connected clients
    # This could be enhanced in the future to filter by broker
    Mqttable.MqttClient.Manager.get_connected_clients()
  end

  # Helper function to determine badge class based on direction
  defp direction_badge_class(direction) do
    case direction do
      "IN" -> "badge-success"
      "OUT" -> "badge-warning"
      _ -> "badge-ghost"
    end
  end

  # Helper function to determine badge class based on message type
  defp message_type_badge_class(type) do
    case type do
      # Publish-related packets
      "PUBLISH" -> "badge-info"
      "PUBACK" -> "badge-info"
      "PUBREC" -> "badge-info"
      "PUBREL" -> "badge-info"
      "PUBCOMP" -> "badge-info"
      # Subscribe-related packets
      "SUBSCRIBE" -> "badge-accent"
      "SUBACK" -> "badge-accent"
      "UNSUBSCRIBE" -> "badge-accent"
      "UNSUBACK" -> "badge-accent"
      # Connection-related packets
      "CONNECT" -> "badge-success"
      "CONNACK" -> "badge-success"
      "DISCONNECT" -> "badge-warning"
      # Ping packets
      "PINGREQ" -> "badge-secondary"
      "PINGRESP" -> "badge-secondary"
      # Auth packet
      "AUTH" -> "badge-primary"
      # Unknown packets
      _ -> "badge-ghost"
    end
  end

  # Helper function to format MQTT properties for display
  defp format_mqtt_properties(properties) when is_map(properties) do
    properties
    |> Enum.map(fn {key, value} -> "#{key}: #{inspect(value)}" end)
    |> Enum.join("\n")
  end

  defp format_mqtt_properties(_), do: ""

  # Helper function to create default publish form
  defp default_publish_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload" => "",
      "qos" => 0,
      "retain" => false
    }
  end

  # Helper function to format timestamp for display (fixed 18 characters)
  defp format_timestamp(timestamp) when is_binary(timestamp) do
    case DateTime.from_iso8601(timestamp) do
      {:ok, dt, _offset} ->
        # Format as MM-DD HH:MM:SS.mmm (exactly 18 characters)
        month = dt.month |> Integer.to_string() |> String.pad_leading(2, "0")
        day = dt.day |> Integer.to_string() |> String.pad_leading(2, "0")
        hour = dt.hour |> Integer.to_string() |> String.pad_leading(2, "0")
        minute = dt.minute |> Integer.to_string() |> String.pad_leading(2, "0")
        second = dt.second |> Integer.to_string() |> String.pad_leading(2, "0")

        millisecond =
          dt.microsecond
          |> elem(0)
          |> div(1000)
          |> Integer.to_string()
          |> String.pad_leading(3, "0")

        "#{month}-#{day} #{hour}:#{minute}:#{second}.#{millisecond}"

      _error ->
        # Ensure fallback is also 18 characters
        timestamp
        |> String.slice(0, 18)
        |> String.pad_trailing(18, " ")
    end
  end

  defp format_timestamp(timestamp) do
    # Ensure N/A is also 18 characters
    (timestamp || "N/A")
    |> String.slice(0, 18)
    |> String.pad_trailing(18, " ")
  end

  # Helper function to get direction icon
  defp direction_icon(direction) do
    case direction do
      "IN" -> "hero-arrow-down-circle"
      "OUT" -> "hero-arrow-up-circle"
      _ -> "hero-minus-circle"
    end
  end

  # Helper function to get direction indicator class (static, no animation)
  defp direction_indicator_class(direction) do
    case direction do
      "IN" -> "trace-message-in"
      "OUT" -> "trace-message-out"
      _ -> "trace-message-neutral"
    end
  end

  # Render packet details based on packet type
  defp render_packet_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-2">
      <!-- Common fields for all packets - 4 columns for maximum compactness -->
      <div class="grid grid-cols-4 gap-2 text-xs">
        <div>
          <strong class="text-gray-600">Timestamp:</strong>
          <div class="text-gray-800 font-mono">{@message.timestamp}</div>
        </div>
        <div>
          <strong class="text-gray-600">Client ID:</strong>
          <div class="text-gray-800 truncate">{@message.client_id || "unknown"}</div>
        </div>
        <div>
          <strong class="text-gray-600">Direction:</strong>
          <div>
            <span class={"badge badge-xs #{direction_badge_class(@message.direction)}"}>
              {@message.direction || "N/A"}
            </span>
          </div>
        </div>
        <div>
          <strong class="text-gray-600">Type:</strong>
          <div>
            <span class={"badge badge-xs #{message_type_badge_class(@message.type)}"}>
              {@message.type}
            </span>
          </div>
        </div>
      </div>
      
    <!-- Packet-specific fields -->
      <%= case @message.type do %>
        <% "PUBLISH" -> %>
          {render_publish_details(@message, @myself, @payload_view_type)}
        <% "SUBSCRIBE" -> %>
          {render_subscribe_details(@message)}
        <% "UNSUBSCRIBE" -> %>
          {render_unsubscribe_details(@message)}
        <% "SUBACK" -> %>
          {render_suback_details(@message)}
        <% "UNSUBACK" -> %>
          {render_unsuback_details(@message)}
        <% type when type in ["PUBACK", "PUBREC", "PUBREL", "PUBCOMP"] -> %>
          {render_pub_ack_details(@message)}
        <% "CONNACK" -> %>
          {render_connack_details(@message)}
        <% "CONNECT" -> %>
          {render_connect_details(@message, @myself, @payload_view_type)}
        <% "DISCONNECT" -> %>
          {render_disconnect_details(@message)}
        <% "AUTH" -> %>
          {render_auth_details(@message)}
        <% _ -> %>
          {render_generic_details(@message, @myself, @payload_view_type)}
      <% end %>
      
    <!-- MQTT Properties (if present) - more compact -->
      <%= if @message.properties && map_size(@message.properties) > 0 do %>
        <div class="mt-1">
          <strong class="text-xs text-gray-600">MQTT Properties:</strong>
          <div class="bg-base-200 p-1 rounded text-xs font-mono leading-tight">
            {format_mqtt_properties(@message.properties)}
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render PUBLISH packet details
  defp render_publish_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-1">
      <!-- Topic and basic info in 3 columns -->
      <div class="grid grid-cols-3 gap-2 text-xs">
        <div class="col-span-2">
          <strong class="text-gray-600">Topic:</strong>
          <div class="text-gray-800 break-all font-mono">{@message.topic || "N/A"}</div>
        </div>
        <div>
          <strong class="text-gray-600">QoS:</strong>
          <div class="text-gray-800">{@message.qos}</div>
        </div>
      </div>
      
    <!-- Retain, Packet ID, and Payload Length in one row -->
      <div class="grid grid-cols-3 gap-2 text-xs">
        <div>
          <strong class="text-gray-600">Retain:</strong>
          <div class="text-gray-800">{if @message.retain, do: "Yes", else: "No"}</div>
        </div>
        <div>
          <strong class="text-gray-600">Packet ID:</strong>
          <div class="text-gray-800">{@message.packet_id || "N/A"}</div>
        </div>
        <div>
          <strong class="text-gray-600">Size:</strong>
          <div class="text-gray-800">{@message.payload_length} bytes</div>
        </div>
      </div>
      
    <!-- Compact payload display with type switcher -->
      <%= if @message.payload && @message.payload != "" do %>
        <div class="text-xs">
          <div class="flex items-center justify-between mb-1">
            <strong class="text-gray-600">Payload:</strong>
            <div class="tabs tabs-xs">
              <button
                class={"tab tab-xs #{if @payload_view_type == "plaintext", do: "tab-active", else: ""}"}
                phx-click="switch_payload_view"
                phx-value-type="plaintext"
                phx-target={@myself}
              >
                Text
              </button>
              <button
                class={"tab tab-xs #{if @payload_view_type == "json", do: "tab-active", else: ""}"}
                phx-click="switch_payload_view"
                phx-value-type="json"
                phx-target={@myself}
              >
                JSON
              </button>
              <button
                class={"tab tab-xs #{if @payload_view_type == "hex", do: "tab-active", else: ""}"}
                phx-click="switch_payload_view"
                phx-value-type="hex"
                phx-target={@myself}
              >
                Hex
              </button>
            </div>
          </div>
          <div class="bg-base-200 p-1 rounded">
            <textarea
              readonly
              class="textarea w-full text-xs font-mono bg-transparent border-none resize-none h-16 leading-tight"
              placeholder="No payload"
            ><%= format_payload_for_display(@message.payload, @payload_view_type || "plaintext") %></textarea>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render SUBSCRIBE packet details
  defp render_subscribe_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-1">
      <!-- Topic filters and packet ID in one compact row -->
      <div class="grid grid-cols-3 gap-2 text-xs">
        <div class="col-span-2">
          <strong class="text-gray-600">Topic Filters:</strong>
          <div class="text-gray-800 break-all font-mono">{@message.topic || "N/A"}</div>
        </div>
        <div>
          <strong class="text-gray-600">Packet ID:</strong>
          <div class="text-gray-800">{@message.packet_id || "N/A"}</div>
        </div>
      </div>

      <%= if @message.extra_info && is_list(@message.extra_info) do %>
        <div class="text-xs">
          <strong class="text-gray-600">Details:</strong>
          <div class="bg-base-200 p-1 rounded mt-1 space-y-0">
            <%= for {topic, opts} <- @message.extra_info do %>
              <div class="flex justify-between items-center py-0.5">
                <span class="font-mono text-xs">{topic}</span>
                <span class="text-gray-600 text-xs">QoS: {inspect(opts)}</span>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render UNSUBSCRIBE packet details
  defp render_unsubscribe_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="grid grid-cols-3 gap-2 text-xs">
      <div class="col-span-2">
        <strong class="text-gray-600">Topic Filters:</strong>
        <div class="text-gray-800 break-all font-mono">{@message.topic || "N/A"}</div>
      </div>
      <div>
        <strong class="text-gray-600">Packet ID:</strong>
        <div class="text-gray-800">{@message.packet_id || "N/A"}</div>
      </div>
    </div>
    """
  end

  # Render SUBACK packet details with error highlighting
  defp render_suback_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-1">
      <!-- Packet ID and Status in one compact row -->
      <div class="grid grid-cols-3 gap-2 text-xs">
        <div>
          <strong class="text-gray-600">Packet ID:</strong>
          <div class="text-gray-800">{@message.packet_id || "N/A"}</div>
        </div>
        <div class="col-span-2">
          <strong class="text-gray-600">Status:</strong>
          <div>
            <span class={get_ack_status_class(@message.reason_code)}>
              {format_suback_status(@message.reason_code)}
            </span>
          </div>
        </div>
      </div>

      <%= if @message.reason_code && is_list(@message.reason_code) do %>
        <div class="text-xs">
          <strong class="text-gray-600">Reason Codes:</strong>
          <div class="space-y-0.5 mt-1">
            <%= for {code, index} <- Enum.with_index(@message.reason_code) do %>
              <div class={"flex justify-between p-1 rounded text-xs #{get_reason_code_class(code)}"}>
                <span>Filter {index + 1}:</span>
                <span class="font-mono">{format_reason_code(code)}</span>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render UNSUBACK packet details with error highlighting
  defp render_unsuback_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-1">
      <!-- Packet ID and Status in one compact row -->
      <div class="grid grid-cols-3 gap-2 text-xs">
        <div>
          <strong class="text-gray-600">Packet ID:</strong>
          <div class="text-gray-800">{@message.packet_id || "N/A"}</div>
        </div>
        <div class="col-span-2">
          <strong class="text-gray-600">Status:</strong>
          <div>
            <span class={get_ack_status_class(@message.reason_code)}>
              {format_unsuback_status(@message.reason_code)}
            </span>
          </div>
        </div>
      </div>

      <%= if @message.reason_code && is_list(@message.reason_code) do %>
        <div class="text-xs">
          <strong class="text-gray-600">Reason Codes:</strong>
          <div class="space-y-0.5 mt-1">
            <%= for {code, index} <- Enum.with_index(@message.reason_code) do %>
              <div class={"flex justify-between p-1 rounded text-xs #{get_reason_code_class(code)}"}>
                <span>Filter {index + 1}:</span>
                <span class="font-mono">{format_reason_code(code)}</span>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render PUBACK/PUBREC/PUBREL/PUBCOMP packet details with error highlighting
  defp render_pub_ack_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-1">
      <!-- Packet ID and Status in one compact row -->
      <div class="grid grid-cols-3 gap-2 text-xs">
        <div>
          <strong class="text-gray-600">Packet ID:</strong>
          <div class="text-gray-800">{@message.packet_id || "N/A"}</div>
        </div>
        <div class="col-span-2">
          <strong class="text-gray-600">Status:</strong>
          <div>
            <span class={get_ack_status_class(@message.reason_code)}>
              {format_pub_ack_status(@message.reason_code)}
            </span>
          </div>
        </div>
      </div>

      <%= if @message.reason_code do %>
        <div class="text-xs">
          <strong class="text-gray-600">Reason Code:</strong>
          <div class={"p-1 rounded mt-1 #{get_reason_code_class(@message.reason_code)}"}>
            <span class="font-mono text-xs">{format_reason_code(@message.reason_code)}</span>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render CONNACK packet details with error highlighting
  defp render_connack_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-1">
      <!-- Status and Session Present in one compact row -->
      <div class="grid grid-cols-3 gap-2 text-xs">
        <div class="col-span-2">
          <strong class="text-gray-600">Status:</strong>
          <div>
            <span class={get_ack_status_class(@message.reason_code)}>
              {format_connack_status(@message.reason_code)}
            </span>
          </div>
        </div>
        <div>
          <strong class="text-gray-600">Session:</strong>
          <div class="text-gray-800">{format_session_present(@message.extra_info)}</div>
        </div>
      </div>

      <%= if @message.reason_code do %>
        <div class="text-xs">
          <strong class="text-gray-600">Reason Code:</strong>
          <div class={"p-1 rounded mt-1 #{get_reason_code_class(@message.reason_code)}"}>
            <span class="font-mono text-xs">{format_reason_code(@message.reason_code)}</span>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render CONNECT packet details
  defp render_connect_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-1">
      <%= if @message.extra_info && is_map(@message.extra_info) do %>
        <!-- Protocol info in 3 columns -->
        <div class="grid grid-cols-3 gap-2 text-xs">
          <div>
            <strong class="text-gray-600">Protocol:</strong>
            <div class="text-gray-800">{@message.extra_info.proto_name || "N/A"}</div>
          </div>
          <div>
            <strong class="text-gray-600">Version:</strong>
            <div class="text-gray-800">{@message.extra_info.proto_ver || "N/A"}</div>
          </div>
          <div>
            <strong class="text-gray-600">Keep Alive:</strong>
            <div class="text-gray-800">{@message.extra_info.keepalive || "N/A"}s</div>
          </div>
        </div>
        
    <!-- Client ID and flags in 3 columns -->
        <div class="grid grid-cols-3 gap-2 text-xs">
          <div>
            <strong class="text-gray-600">Client ID:</strong>
            <div class="text-gray-800 break-all font-mono">
              {@message.extra_info.clientid || "N/A"}
            </div>
          </div>
          <div>
            <strong class="text-gray-600">Clean Start:</strong>
            <div class="text-gray-800">
              {if @message.extra_info.clean_start, do: "Yes", else: "No"}
            </div>
          </div>
          <div>
            <strong class="text-gray-600">Username:</strong>
            <div class="text-gray-800">{@message.extra_info.username || "N/A"}</div>
          </div>
        </div>

        <%= if @message.extra_info.will_flag do %>
          <div class="text-xs">
            <strong class="text-gray-600">Will Message:</strong>
            <div class="bg-base-200 p-1 rounded mt-1">
              <div class="grid grid-cols-3 gap-1 text-xs">
                <div>
                  <strong>Topic:</strong> {@message.extra_info.will_topic || "N/A"}
                </div>
                <div>
                  <strong>QoS:</strong> {@message.extra_info.will_qos || "N/A"}
                </div>
                <div>
                  <strong>Retain:</strong> {if @message.extra_info.will_retain, do: "Yes", else: "No"}
                </div>
              </div>
              <%= if @message.extra_info.will_payload do %>
                <div class="mt-1">
                  <div class="flex items-center justify-between">
                    <strong>Payload:</strong>
                    <div class="tabs tabs-xs">
                      <button
                        class={"tab tab-xs #{if @payload_view_type == "plaintext", do: "tab-active", else: ""}"}
                        phx-click="switch_payload_view"
                        phx-value-type="plaintext"
                        phx-target={@myself}
                      >
                        Text
                      </button>
                      <button
                        class={"tab tab-xs #{if @payload_view_type == "json", do: "tab-active", else: ""}"}
                        phx-click="switch_payload_view"
                        phx-value-type="json"
                        phx-target={@myself}
                      >
                        JSON
                      </button>
                      <button
                        class={"tab tab-xs #{if @payload_view_type == "hex", do: "tab-active", else: ""}"}
                        phx-click="switch_payload_view"
                        phx-value-type="hex"
                        phx-target={@myself}
                      >
                        Hex
                      </button>
                    </div>
                  </div>
                  <div class="font-mono bg-base-300 p-1 rounded text-xs leading-tight mt-1">
                    {format_payload_for_display(
                      @message.extra_info.will_payload,
                      @payload_view_type || "plaintext"
                    )}
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end

  # Render DISCONNECT packet details
  defp render_disconnect_details(message) do
    assigns = %{message: message}

    ~H"""
    <%= if @message.reason_code do %>
      <div class="text-xs">
        <strong class="text-gray-600">Reason Code:</strong>
        <div class={"p-1 rounded mt-1 #{get_reason_code_class(@message.reason_code)}"}>
          <span class="font-mono text-xs">{format_reason_code(@message.reason_code)}</span>
        </div>
      </div>
    <% end %>
    """
  end

  # Render AUTH packet details
  defp render_auth_details(message) do
    assigns = %{message: message}

    ~H"""
    <%= if @message.reason_code do %>
      <div class="text-xs">
        <strong class="text-gray-600">Reason Code:</strong>
        <div class={"p-1 rounded mt-1 #{get_reason_code_class(@message.reason_code)}"}>
          <span class="font-mono text-xs">{format_reason_code(@message.reason_code)}</span>
        </div>
      </div>
    <% end %>
    """
  end

  # Render generic packet details for unknown types
  defp render_generic_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-1">
      <%= if @message.packet_id do %>
        <div class="text-xs">
          <strong class="text-gray-600">Packet ID:</strong>
          <div class="text-gray-800">{@message.packet_id}</div>
        </div>
      <% end %>

      <%= if @message.payload && @message.payload != "" do %>
        <div class="text-xs">
          <div class="flex items-center justify-between mb-1">
            <strong class="text-gray-600">Payload:</strong>
            <div class="tabs tabs-xs">
              <button
                class={"tab tab-xs #{if @payload_view_type == "plaintext", do: "tab-active", else: ""}"}
                phx-click="switch_payload_view"
                phx-value-type="plaintext"
                phx-target={@myself}
              >
                Text
              </button>
              <button
                class={"tab tab-xs #{if @payload_view_type == "json", do: "tab-active", else: ""}"}
                phx-click="switch_payload_view"
                phx-value-type="json"
                phx-target={@myself}
              >
                JSON
              </button>
              <button
                class={"tab tab-xs #{if @payload_view_type == "hex", do: "tab-active", else: ""}"}
                phx-click="switch_payload_view"
                phx-value-type="hex"
                phx-target={@myself}
              >
                Hex
              </button>
            </div>
          </div>
          <div class="bg-base-200 p-1 rounded">
            <textarea
              readonly
              class="textarea w-full text-xs font-mono bg-transparent border-none resize-none h-16 leading-tight"
              placeholder="No payload"
            ><%= format_payload_for_display(@message.payload, @payload_view_type || "plaintext") %></textarea>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Helper functions for error highlighting and status formatting

  # Get CSS class for ACK status (success/error highlighting)
  defp get_ack_status_class(reason_code) when is_integer(reason_code) do
    if reason_code == 0 do
      "text-xs badge badge-success"
    else
      "text-xs badge badge-error"
    end
  end

  defp get_ack_status_class(reason_codes) when is_list(reason_codes) do
    if Enum.all?(reason_codes, &(&1 == 0)) do
      "text-xs badge badge-success"
    else
      "text-xs badge badge-error"
    end
  end

  defp get_ack_status_class(_), do: "text-xs badge badge-ghost"

  # Get CSS class for individual reason codes
  defp get_reason_code_class(reason_code) when is_integer(reason_code) do
    if reason_code == 0 do
      "bg-success/20 text-success"
    else
      "bg-error/20 text-error"
    end
  end

  defp get_reason_code_class(_), do: "bg-base-200 text-base-content"

  # Format SUBACK status
  defp format_suback_status(reason_codes) when is_list(reason_codes) do
    success_count = Enum.count(reason_codes, &(&1 == 0))
    total_count = length(reason_codes)

    if success_count == total_count do
      "All Subscriptions Successful"
    else
      "#{success_count}/#{total_count} Subscriptions Successful"
    end
  end

  defp format_suback_status(_), do: "Unknown"

  # Format UNSUBACK status
  defp format_unsuback_status(reason_codes) when is_list(reason_codes) do
    success_count = Enum.count(reason_codes, &(&1 == 0))
    total_count = length(reason_codes)

    if success_count == total_count do
      "All Unsubscriptions Successful"
    else
      "#{success_count}/#{total_count} Unsubscriptions Successful"
    end
  end

  defp format_unsuback_status(_), do: "Unknown"

  # Format PUBACK/PUBREC/PUBREL/PUBCOMP status
  defp format_pub_ack_status(reason_code) when is_integer(reason_code) do
    case reason_code do
      0 -> "Success"
      16 -> "No matching subscribers"
      128 -> "Unspecified error"
      129 -> "Implementation specific error"
      135 -> "Not authorized"
      144 -> "Topic Name invalid"
      145 -> "Packet identifier in use"
      146 -> "Packet identifier not found"
      147 -> "Receive Maximum exceeded"
      148 -> "Topic Alias invalid"
      149 -> "Packet too large"
      150 -> "Message rate too high"
      151 -> "Quota exceeded"
      152 -> "Administrative action"
      153 -> "Payload format invalid"
      154 -> "Retain not supported"
      155 -> "QoS not supported"
      156 -> "Use another server"
      157 -> "Server moved"
      158 -> "Shared Subscriptions not supported"
      159 -> "Connection rate exceeded"
      160 -> "Maximum connect time"
      161 -> "Subscription Identifiers not supported"
      162 -> "Wildcard Subscriptions not supported"
      _ -> "Unknown (#{reason_code})"
    end
  end

  defp format_pub_ack_status(_), do: "Unknown"

  # Format CONNACK status
  defp format_connack_status(reason_code) when is_integer(reason_code) do
    case reason_code do
      0 -> "Connection Accepted"
      128 -> "Unspecified error"
      129 -> "Malformed Packet"
      130 -> "Protocol Error"
      131 -> "Implementation specific error"
      132 -> "Unsupported Protocol Version"
      133 -> "Client Identifier not valid"
      134 -> "Bad User Name or Password"
      135 -> "Not authorized"
      136 -> "Server unavailable"
      137 -> "Server busy"
      138 -> "Banned"
      140 -> "Bad authentication method"
      144 -> "Topic Name invalid"
      149 -> "Packet too large"
      151 -> "Quota exceeded"
      153 -> "Payload format invalid"
      154 -> "Retain not supported"
      155 -> "QoS not supported"
      156 -> "Use another server"
      157 -> "Server moved"
      159 -> "Connection rate exceeded"
      _ -> "Unknown (#{reason_code})"
    end
  end

  defp format_connack_status(_), do: "Unknown"

  # Format reason codes with descriptive text
  defp format_reason_code(reason_code) when is_integer(reason_code) do
    case reason_code do
      0 -> "0x00 - Success"
      1 -> "0x01 - Normal disconnection"
      2 -> "0x02 - Granted QoS 0"
      4 -> "0x04 - Disconnect with Will Message"
      16 -> "0x10 - No matching subscribers"
      17 -> "0x11 - No subscription existed"
      24 -> "0x18 - Continue authentication"
      25 -> "0x19 - Re-authenticate"
      128 -> "0x80 - Unspecified error"
      129 -> "0x81 - Malformed Packet"
      130 -> "0x82 - Protocol Error"
      131 -> "0x83 - Implementation specific error"
      132 -> "0x84 - Unsupported Protocol Version"
      133 -> "0x85 - Client Identifier not valid"
      134 -> "0x86 - Bad User Name or Password"
      135 -> "0x87 - Not authorized"
      136 -> "0x88 - Server unavailable"
      137 -> "0x89 - Server busy"
      138 -> "0x8A - Banned"
      139 -> "0x8B - Server shutting down"
      140 -> "0x8C - Bad authentication method"
      141 -> "0x8D - Keep Alive timeout"
      142 -> "0x8E - Session taken over"
      143 -> "0x8F - Topic Filter invalid"
      144 -> "0x90 - Topic Name invalid"
      145 -> "0x91 - Packet identifier in use"
      146 -> "0x92 - Packet identifier not found"
      147 -> "0x93 - Receive Maximum exceeded"
      148 -> "0x94 - Topic Alias invalid"
      149 -> "0x95 - Packet too large"
      150 -> "0x96 - Message rate too high"
      151 -> "0x97 - Quota exceeded"
      152 -> "0x98 - Administrative action"
      153 -> "0x99 - Payload format invalid"
      154 -> "0x9A - Retain not supported"
      155 -> "0x9B - QoS not supported"
      156 -> "0x9C - Use another server"
      157 -> "0x9D - Server moved"
      158 -> "0x9E - Shared Subscriptions not supported"
      159 -> "0x9F - Connection rate exceeded"
      160 -> "0xA0 - Maximum connect time"
      161 -> "0xA1 - Subscription Identifiers not supported"
      162 -> "0xA2 - Wildcard Subscriptions not supported"
      _ -> "0x#{Integer.to_string(reason_code, 16) |> String.upcase()} - Unknown"
    end
  end

  defp format_reason_code(reason_code), do: "#{inspect(reason_code)} - Unknown"

  # Format session present flag from CONNACK
  defp format_session_present(ack_flags) when is_integer(ack_flags) do
    if (ack_flags &&& 1) == 1, do: "Yes", else: "No"
  end

  defp format_session_present(_), do: "N/A"

  # Format payload for different display types
  defp format_payload_for_display(payload, view_type) when is_binary(payload) do
    case view_type do
      "json" ->
        format_payload_as_json(payload)

      "hex" ->
        format_payload_as_hex(payload)

      _ ->
        # Default to plaintext
        payload
    end
  end

  defp format_payload_for_display(payload, _view_type), do: inspect(payload)

  # Format payload as JSON with pretty printing
  defp format_payload_as_json(payload) do
    case Jason.decode(payload) do
      {:ok, decoded} ->
        case Jason.encode(decoded, pretty: true) do
          {:ok, pretty_json} -> pretty_json
          {:error, _} -> payload
        end

      {:error, _} ->
        # If it's not valid JSON, return original payload with a note
        "# Not valid JSON\n#{payload}"
    end
  end

  # Format payload as hexadecimal
  defp format_payload_as_hex(payload) do
    payload
    |> :binary.bin_to_list()
    |> Enum.chunk_every(16)
    |> Enum.with_index()
    |> Enum.map(fn {chunk, index} ->
      offset = String.pad_leading(Integer.to_string(index * 16, 16), 8, "0")

      hex_part =
        chunk
        |> Enum.map(&String.pad_leading(Integer.to_string(&1, 16), 2, "0"))
        |> Enum.join(" ")
        |> String.pad_trailing(47, " ")

      ascii_part =
        chunk
        |> Enum.map(fn byte ->
          if byte >= 32 and byte <= 126, do: <<byte>>, else: "."
        end)
        |> Enum.join("")

      "#{offset}  #{hex_part}  |#{ascii_part}|"
    end)
    |> Enum.join("\n")
  end
end
